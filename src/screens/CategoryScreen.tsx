import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  FlatList,
} from 'react-native';

const CategoryScreen = () => {
  const [selectedCategory, setSelectedCategory] = useState('服装');

  const categories = [
    { id: 1, name: '服装', icon: '👕' },
    { id: 2, name: '数码', icon: '📱' },
    { id: 3, name: '家居', icon: '🏠' },
    { id: 4, name: '美妆', icon: '💄' },
    { id: 5, name: '运动', icon: '⚽' },
    { id: 6, name: '图书', icon: '📚' },
    { id: 7, name: '食品', icon: '🍎' },
    { id: 8, name: '母婴', icon: '🍼' },
  ];

  const getProductsByCategory = (category: string) => {
    const productMap: { [key: string]: any[] } = {
      '服装': [
        { id: 1, name: '时尚T恤', price: '¥99', icon: '👕' },
        { id: 2, name: '牛仔裤', price: '¥199', icon: '👖' },
        { id: 3, name: '连衣裙', price: '¥299', icon: '👗' },
        { id: 4, name: '运动鞋', price: '¥399', icon: '👟' },
      ],
      '数码': [
        { id: 5, name: '智能手机', price: '¥2999', icon: '📱' },
        { id: 6, name: '平板电脑', price: '¥1999', icon: '📱' },
        { id: 7, name: '笔记本电脑', price: '¥4999', icon: '💻' },
        { id: 8, name: '智能手表', price: '¥1299', icon: '⌚' },
      ],
      '家居': [
        { id: 9, name: '舒适沙发', price: '¥1999', icon: '🛋️' },
        { id: 10, name: '餐桌椅', price: '¥899', icon: '🪑' },
        { id: 11, name: '床上用品', price: '¥299', icon: '🛏️' },
        { id: 12, name: '装饰画', price: '¥199', icon: '🖼️' },
      ],
      '美妆': [
        { id: 13, name: '护肤套装', price: '¥299', icon: '🧴' },
        { id: 14, name: '口红', price: '¥99', icon: '💄' },
        { id: 15, name: '面膜', price: '¥59', icon: '🧴' },
        { id: 16, name: '香水', price: '¥399', icon: '🧴' },
      ],
    };
    return productMap[category] || [];
  };

  const renderCategoryItem = ({ item }: { item: any }) => (
    <TouchableOpacity
      style={[
        styles.categoryItem,
        selectedCategory === item.name && styles.selectedCategoryItem,
      ]}
      onPress={() => setSelectedCategory(item.name)}>
      <Text style={styles.categoryIcon}>{item.icon}</Text>
      <Text
        style={[
          styles.categoryName,
          selectedCategory === item.name && styles.selectedCategoryName,
        ]}>
        {item.name}
      </Text>
    </TouchableOpacity>
  );

  const renderProductItem = ({ item }: { item: any }) => (
    <TouchableOpacity style={styles.productItem}>
      <View style={styles.productImage}>
        <Text style={styles.productIcon}>{item.icon}</Text>
      </View>
      <Text style={styles.productName}>{item.name}</Text>
      <Text style={styles.productPrice}>{item.price}</Text>
    </TouchableOpacity>
  );

  return (
    <View style={styles.container}>
      {/* 左侧分类列表 */}
      <View style={styles.categoryContainer}>
        <FlatList
          data={categories}
          renderItem={renderCategoryItem}
          keyExtractor={item => item.id.toString()}
          showsVerticalScrollIndicator={false}
        />
      </View>

      {/* 右侧商品列表 */}
      <View style={styles.productContainer}>
        <Text style={styles.sectionTitle}>{selectedCategory}</Text>
        <FlatList
          data={getProductsByCategory(selectedCategory)}
          renderItem={renderProductItem}
          keyExtractor={item => item.id.toString()}
          numColumns={2}
          showsVerticalScrollIndicator={false}
          contentContainerStyle={styles.productList}
        />
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    flexDirection: 'row',
    backgroundColor: '#f5f5f5',
  },
  categoryContainer: {
    width: 100,
    backgroundColor: 'white',
    borderRightWidth: 1,
    borderRightColor: '#e0e0e0',
  },
  categoryItem: {
    alignItems: 'center',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  selectedCategoryItem: {
    backgroundColor: '#f0f8ff',
    borderRightWidth: 3,
    borderRightColor: '#007AFF',
  },
  categoryIcon: {
    fontSize: 24,
    marginBottom: 4,
  },
  categoryName: {
    fontSize: 12,
    color: '#666',
    textAlign: 'center',
  },
  selectedCategoryName: {
    color: '#007AFF',
    fontWeight: 'bold',
  },
  productContainer: {
    flex: 1,
    padding: 16,
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    marginBottom: 16,
    color: '#333',
  },
  productList: {
    paddingBottom: 20,
  },
  productItem: {
    width: '48%',
    backgroundColor: 'white',
    borderRadius: 12,
    padding: 12,
    marginBottom: 16,
    marginHorizontal: '1%',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  productImage: {
    height: 100,
    backgroundColor: '#f8f8f8',
    borderRadius: 8,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 8,
  },
  productIcon: {
    fontSize: 40,
  },
  productName: {
    fontSize: 14,
    fontWeight: '500',
    color: '#333',
    marginBottom: 4,
  },
  productPrice: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#007AFF',
  },
});

export default CategoryScreen;
