import React, { useEffect, useRef } from 'react';
import {
  View,
  Text,
  StyleSheet,
  Animated,
  Dimensions,
} from 'react-native';
import { useNavigation } from '@react-navigation/native';

const { width, height } = Dimensions.get('window');

const SplashScreen = () => {
  const navigation = useNavigation();
  const fadeAnim = useRef(new Animated.Value(0)).current;
  const scaleAnim = useRef(new Animated.Value(0.8)).current;
  const dot1Anim = useRef(new Animated.Value(0)).current;
  const dot2Anim = useRef(new Animated.Value(0)).current;
  const dot3Anim = useRef(new Animated.Value(0)).current;

  useEffect(() => {
    // 启动动画 - 缩短时间
    Animated.parallel([
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 500, // 从1000ms缩短到500ms
        useNativeDriver: true,
      }),
      Animated.spring(scaleAnim, {
        toValue: 1,
        tension: 100, // 增加张力，加快动画
        friction: 6,  // 减少摩擦，加快动画
        useNativeDriver: true,
      }),
    ]).start();

    // 加载点动画
    const createDotAnimation = (animValue: Animated.Value, delay: number) => {
      return Animated.loop(
        Animated.sequence([
          Animated.timing(animValue, {
            toValue: 1,
            duration: 400,
            delay,
            useNativeDriver: true,
          }),
          Animated.timing(animValue, {
            toValue: 0,
            duration: 400,
            useNativeDriver: true,
          }),
        ])
      );
    };

    // 启动加载点动画 - 缩短延迟时间
    setTimeout(() => {
      Animated.parallel([
        createDotAnimation(dot1Anim, 0),
        createDotAnimation(dot2Anim, 150), // 从200ms缩短到150ms
        createDotAnimation(dot3Anim, 300), // 从400ms缩短到300ms
      ]).start();
    }, 500); // 从1000ms缩短到500ms

    // 模拟初始化操作
    const initializeApp = async () => {
      // 这里可以添加实际的初始化逻辑，比如：
      // - 检查用户登录状态
      // - 加载必要的配置数据
      // - 预加载资源
      // - 检查应用更新等

      return new Promise(resolve => {
        setTimeout(() => {
          resolve(true);
        }, 1500); // 从2.5秒缩短到1.5秒
      });
    };

    initializeApp().then(() => {
      // 初始化完成后，直接跳转到主页面，去掉动画效果
      navigation.reset({
        index: 0,
        routes: [{ name: 'Main' as never }],
      });
    });
  }, [navigation, fadeAnim, scaleAnim, dot1Anim, dot2Anim, dot3Anim]);

  return (
    <View style={styles.container}>
      <Animated.View
        style={[
          styles.logoContainer,
          {
            opacity: fadeAnim,
            transform: [{ scale: scaleAnim }],
          },
        ]}>
        {/* 商城Logo */}
        <View style={styles.logoWrapper}>
          <Text style={styles.logoIcon}>🛍️</Text>
          <Text style={styles.logoText}>购物商城</Text>
          <Text style={styles.logoSubText}>发现更多精彩</Text>
        </View>

        {/* 加载指示器 */}
        <View style={styles.loadingContainer}>
          <View style={styles.loadingDots}>
            <Animated.View
              style={[
                styles.dot,
                { opacity: dot1Anim }
              ]}
            />
            <Animated.View
              style={[
                styles.dot,
                { opacity: dot2Anim }
              ]}
            />
            <Animated.View
              style={[
                styles.dot,
                { opacity: dot3Anim }
              ]}
            />
          </View>
        </View>
      </Animated.View>

      {/* 版权信息 */}
      <View style={styles.footerContainer}>
        <Text style={styles.footerText}>© 2024 购物商城</Text>
        <Text style={styles.versionText}>v1.0.0</Text>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#007AFF',
    justifyContent: 'center',
    alignItems: 'center',
  },
  logoContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    flex: 1,
  },
  logoWrapper: {
    alignItems: 'center',
    marginBottom: 60,
  },
  logoIcon: {
    fontSize: 120,
    marginBottom: 20,
    textShadowColor: 'rgba(0, 0, 0, 0.1)',
    textShadowOffset: { width: 0, height: 2 },
    textShadowRadius: 4,
  },
  logoText: {
    fontSize: 36,
    fontWeight: 'bold',
    color: 'white',
    marginBottom: 8,
    textShadowColor: 'rgba(0, 0, 0, 0.2)',
    textShadowOffset: { width: 0, height: 1 },
    textShadowRadius: 2,
  },
  logoSubText: {
    fontSize: 18,
    color: 'rgba(255, 255, 255, 0.9)',
    fontWeight: '300',
  },
  loadingContainer: {
    alignItems: 'center',
  },
  loadingDots: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  dot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    backgroundColor: 'rgba(255, 255, 255, 0.8)',
    marginHorizontal: 4,
  },

  footerContainer: {
    position: 'absolute',
    bottom: 50,
    alignItems: 'center',
  },
  footerText: {
    fontSize: 14,
    color: 'rgba(255, 255, 255, 0.8)',
    marginBottom: 4,
  },
  versionText: {
    fontSize: 12,
    color: 'rgba(255, 255, 255, 0.6)',
  },
});

export default SplashScreen;
