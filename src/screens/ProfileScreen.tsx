import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Alert,
} from 'react-native';
import { useNavigation } from '@react-navigation/native';

const ProfileScreen = () => {
  const navigation = useNavigation();
  const [isLoggedIn, setIsLoggedIn] = useState(false);
  const [userInfo, setUserInfo] = useState({
    name: '用户名',
    phone: '138****8888',
    avatar: '👤',
  });

  const menuItems = [
    { id: 1, title: '我的订单', icon: '📦', subtitle: '查看全部订单' },
    { id: 2, title: '收货地址', icon: '📍', subtitle: '管理收货地址' },
    { id: 3, title: '优惠券', icon: '🎫', subtitle: '查看可用优惠券' },
    { id: 4, title: '积分商城', icon: '⭐', subtitle: '积分兑换好礼' },
    { id: 5, title: '客服中心', icon: '💬', subtitle: '在线客服帮助' },
    { id: 6, title: '设置', icon: '⚙️', subtitle: '账户与隐私设置' },
  ];

  const handleLogin = () => {
    // 导航到登录页面
    navigation.navigate('Login' as never);
  };

  const handleLogout = () => {
    Alert.alert(
      '确认退出',
      '确定要退出登录吗？',
      [
        { text: '取消', style: 'cancel' },
        {
          text: '退出',
          style: 'destructive',
          onPress: () => {
            setIsLoggedIn(false);
            Alert.alert('提示', '已退出登录');
          },
        },
      ]
    );
  };

  const handleMenuPress = (item: any) => {
    if (!isLoggedIn && item.id !== 5) {
      Alert.alert('提示', '请先登录', [
        { text: '取消', style: 'cancel' },
        { text: '去登录', onPress: handleLogin },
      ]);
      return;
    }
    Alert.alert('提示', `点击了${item.title}`);
  };

  return (
    <ScrollView style={styles.container}>
      {/* 用户信息区域 */}
      <View style={styles.userSection}>
        {isLoggedIn ? (
          <View style={styles.userInfo}>
            <View style={styles.avatar}>
              <Text style={styles.avatarText}>{userInfo.avatar}</Text>
            </View>
            <View style={styles.userDetails}>
              <Text style={styles.userName}>{userInfo.name}</Text>
              <Text style={styles.userPhone}>{userInfo.phone}</Text>
            </View>
            <TouchableOpacity style={styles.logoutButton} onPress={handleLogout}>
              <Text style={styles.logoutButtonText}>退出</Text>
            </TouchableOpacity>
          </View>
        ) : (
          <TouchableOpacity style={styles.loginPrompt} onPress={handleLogin}>
            <View style={styles.avatar}>
              <Text style={styles.avatarText}>👤</Text>
            </View>
            <View style={styles.loginInfo}>
              <Text style={styles.loginText}>点击登录</Text>
              <Text style={styles.loginSubText}>登录后享受更多服务</Text>
            </View>
            <Text style={styles.loginArrow}>›</Text>
          </TouchableOpacity>
        )}
      </View>

      {/* 快捷功能区域 */}
      <View style={styles.quickActions}>
        <TouchableOpacity style={styles.quickActionItem}>
          <Text style={styles.quickActionIcon}>💰</Text>
          <Text style={styles.quickActionText}>余额</Text>
          <Text style={styles.quickActionValue}>¥0.00</Text>
        </TouchableOpacity>
        <TouchableOpacity style={styles.quickActionItem}>
          <Text style={styles.quickActionIcon}>🎫</Text>
          <Text style={styles.quickActionText}>优惠券</Text>
          <Text style={styles.quickActionValue}>0张</Text>
        </TouchableOpacity>
        <TouchableOpacity style={styles.quickActionItem}>
          <Text style={styles.quickActionIcon}>⭐</Text>
          <Text style={styles.quickActionText}>积分</Text>
          <Text style={styles.quickActionValue}>0</Text>
        </TouchableOpacity>
      </View>

      {/* 菜单列表 */}
      <View style={styles.menuSection}>
        {menuItems.map(item => (
          <TouchableOpacity
            key={item.id}
            style={styles.menuItem}
            onPress={() => handleMenuPress(item)}>
            <Text style={styles.menuIcon}>{item.icon}</Text>
            <View style={styles.menuContent}>
              <Text style={styles.menuTitle}>{item.title}</Text>
              <Text style={styles.menuSubtitle}>{item.subtitle}</Text>
            </View>
            <Text style={styles.menuArrow}>›</Text>
          </TouchableOpacity>
        ))}
      </View>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  userSection: {
    backgroundColor: 'white',
    margin: 16,
    borderRadius: 12,
    padding: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  userInfo: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  loginPrompt: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  avatar: {
    width: 60,
    height: 60,
    backgroundColor: '#f0f0f0',
    borderRadius: 30,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 16,
  },
  avatarText: {
    fontSize: 32,
  },
  userDetails: {
    flex: 1,
  },
  loginInfo: {
    flex: 1,
  },
  userName: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 4,
  },
  userPhone: {
    fontSize: 16,
    color: '#666',
  },
  loginText: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 4,
  },
  loginSubText: {
    fontSize: 14,
    color: '#666',
  },
  logoutButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    backgroundColor: '#f0f0f0',
    borderRadius: 20,
  },
  logoutButtonText: {
    fontSize: 14,
    color: '#666',
  },
  loginArrow: {
    fontSize: 24,
    color: '#ccc',
  },
  quickActions: {
    flexDirection: 'row',
    backgroundColor: 'white',
    marginHorizontal: 16,
    marginBottom: 16,
    borderRadius: 12,
    padding: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  quickActionItem: {
    flex: 1,
    alignItems: 'center',
  },
  quickActionIcon: {
    fontSize: 24,
    marginBottom: 8,
  },
  quickActionText: {
    fontSize: 14,
    color: '#666',
    marginBottom: 4,
  },
  quickActionValue: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
  },
  menuSection: {
    backgroundColor: 'white',
    marginHorizontal: 16,
    marginBottom: 16,
    borderRadius: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  menuItem: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  menuIcon: {
    fontSize: 24,
    marginRight: 16,
  },
  menuContent: {
    flex: 1,
  },
  menuTitle: {
    fontSize: 16,
    fontWeight: '500',
    color: '#333',
    marginBottom: 2,
  },
  menuSubtitle: {
    fontSize: 12,
    color: '#999',
  },
  menuArrow: {
    fontSize: 20,
    color: '#ccc',
  },
});

export default ProfileScreen;
