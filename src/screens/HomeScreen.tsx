import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Image,
} from 'react-native';

const HomeScreen = () => {
  const categories = [
    { id: 1, name: '服装', icon: '👕' },
    { id: 2, name: '数码', icon: '📱' },
    { id: 3, name: '家居', icon: '🏠' },
    { id: 4, name: '美妆', icon: '💄' },
  ];

  const products = [
    { id: 1, name: '时尚T恤', price: '¥99', image: '👕' },
    { id: 2, name: '智能手机', price: '¥2999', image: '📱' },
    { id: 3, name: '舒适沙发', price: '¥1999', image: '🛋️' },
    { id: 4, name: '护肤套装', price: '¥299', image: '🧴' },
  ];

  return (
    <ScrollView style={styles.container}>
      {/* 轮播图区域 */}
      <View style={styles.bannerContainer}>
        <View style={styles.banner}>
          <Text style={styles.bannerText}>欢迎来到购物商城</Text>
          <Text style={styles.bannerSubText}>发现更多精彩商品</Text>
        </View>
      </View>

      {/* 分类导航 */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>商品分类</Text>
        <View style={styles.categoriesContainer}>
          {categories.map(category => (
            <TouchableOpacity key={category.id} style={styles.categoryItem}>
              <Text style={styles.categoryIcon}>{category.icon}</Text>
              <Text style={styles.categoryName}>{category.name}</Text>
            </TouchableOpacity>
          ))}
        </View>
      </View>

      {/* 推荐商品 */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>推荐商品</Text>
        <View style={styles.productsContainer}>
          {products.map(product => (
            <TouchableOpacity key={product.id} style={styles.productItem}>
              <View style={styles.productImage}>
                <Text style={styles.productIcon}>{product.image}</Text>
              </View>
              <Text style={styles.productName}>{product.name}</Text>
              <Text style={styles.productPrice}>{product.price}</Text>
            </TouchableOpacity>
          ))}
        </View>
      </View>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  bannerContainer: {
    margin: 16,
    marginBottom: 20,
  },
  banner: {
    backgroundColor: '#007AFF',
    borderRadius: 12,
    padding: 24,
    alignItems: 'center',
  },
  bannerText: {
    color: 'white',
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 8,
  },
  bannerSubText: {
    color: 'white',
    fontSize: 16,
    opacity: 0.9,
  },
  section: {
    marginBottom: 24,
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    marginHorizontal: 16,
    marginBottom: 16,
    color: '#333',
  },
  categoriesContainer: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    paddingHorizontal: 16,
  },
  categoryItem: {
    alignItems: 'center',
    padding: 16,
    backgroundColor: 'white',
    borderRadius: 12,
    minWidth: 70,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  categoryIcon: {
    fontSize: 32,
    marginBottom: 8,
  },
  categoryName: {
    fontSize: 14,
    color: '#333',
    fontWeight: '500',
  },
  productsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    paddingHorizontal: 16,
    justifyContent: 'space-between',
  },
  productItem: {
    width: '48%',
    backgroundColor: 'white',
    borderRadius: 12,
    padding: 12,
    marginBottom: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  productImage: {
    height: 120,
    backgroundColor: '#f8f8f8',
    borderRadius: 8,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 8,
  },
  productIcon: {
    fontSize: 48,
  },
  productName: {
    fontSize: 16,
    fontWeight: '500',
    color: '#333',
    marginBottom: 4,
  },
  productPrice: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#007AFF',
  },
});

export default HomeScreen;
