import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TextInput,
  TouchableOpacity,
  Alert,
  KeyboardAvoidingView,
  Platform,
  ScrollView,
} from 'react-native';
import { useNavigation } from '@react-navigation/native';

const LoginScreen = () => {
  const navigation = useNavigation();
  const [phone, setPhone] = useState('');
  const [password, setPassword] = useState('');
  const [isLoading, setIsLoading] = useState(false);

  const handleLogin = async () => {
    if (!phone.trim()) {
      Alert.alert('提示', '请输入手机号');
      return;
    }

    if (!password.trim()) {
      Alert.alert('提示', '请输入密码');
      return;
    }

    // 简单的手机号格式验证
    const phoneRegex = /^1[3-9]\d{9}$/;
    if (!phoneRegex.test(phone)) {
      Alert.alert('提示', '请输入正确的手机号格式');
      return;
    }

    if (password.length < 6) {
      Alert.alert('提示', '密码长度不能少于6位');
      return;
    }

    setIsLoading(true);

    // 模拟登录请求
    setTimeout(() => {
      setIsLoading(false);
      
      // 模拟登录成功
      if (phone === '13888888888' && password === '123456') {
        Alert.alert('登录成功', '欢迎回来！', [
          {
            text: '确定',
            onPress: () => {
              // 返回到我的页面
              navigation.goBack();
            },
          },
        ]);
      } else {
        Alert.alert('登录失败', '手机号或密码错误\n\n提示：测试账号\n手机号：13888888888\n密码：123456');
      }
    }, 1500);
  };

  const handleRegister = () => {
    Alert.alert('提示', '注册功能开发中...');
  };

  const handleForgotPassword = () => {
    Alert.alert('提示', '忘记密码功能开发中...');
  };

  const handleQuickLogin = (type: string) => {
    Alert.alert('提示', `${type}登录功能开发中...`);
  };

  return (
    <KeyboardAvoidingView
      style={styles.container}
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}>
      <ScrollView contentContainerStyle={styles.scrollContainer}>
        {/* Logo区域 */}
        <View style={styles.logoContainer}>
          <Text style={styles.logo}>🛍️</Text>
          <Text style={styles.appName}>购物商城</Text>
          <Text style={styles.slogan}>发现更多精彩</Text>
        </View>

        {/* 登录表单 */}
        <View style={styles.formContainer}>
          <View style={styles.inputContainer}>
            <Text style={styles.inputIcon}>📱</Text>
            <TextInput
              style={styles.input}
              placeholder="请输入手机号"
              placeholderTextColor="#999"
              value={phone}
              onChangeText={setPhone}
              keyboardType="phone-pad"
              maxLength={11}
            />
          </View>

          <View style={styles.inputContainer}>
            <Text style={styles.inputIcon}>🔒</Text>
            <TextInput
              style={styles.input}
              placeholder="请输入密码"
              placeholderTextColor="#999"
              value={password}
              onChangeText={setPassword}
              secureTextEntry
              maxLength={20}
            />
          </View>

          <TouchableOpacity
            style={[styles.loginButton, isLoading && styles.loginButtonDisabled]}
            onPress={handleLogin}
            disabled={isLoading}>
            <Text style={styles.loginButtonText}>
              {isLoading ? '登录中...' : '登录'}
            </Text>
          </TouchableOpacity>

          <View style={styles.linkContainer}>
            <TouchableOpacity onPress={handleForgotPassword}>
              <Text style={styles.linkText}>忘记密码？</Text>
            </TouchableOpacity>
            <TouchableOpacity onPress={handleRegister}>
              <Text style={styles.linkText}>立即注册</Text>
            </TouchableOpacity>
          </View>
        </View>

        {/* 第三方登录 */}
        <View style={styles.thirdPartyContainer}>
          <Text style={styles.thirdPartyTitle}>其他登录方式</Text>
          <View style={styles.thirdPartyButtons}>
            <TouchableOpacity
              style={styles.thirdPartyButton}
              onPress={() => handleQuickLogin('微信')}>
              <Text style={styles.thirdPartyIcon}>💬</Text>
              <Text style={styles.thirdPartyText}>微信</Text>
            </TouchableOpacity>
            <TouchableOpacity
              style={styles.thirdPartyButton}
              onPress={() => handleQuickLogin('QQ')}>
              <Text style={styles.thirdPartyIcon}>🐧</Text>
              <Text style={styles.thirdPartyText}>QQ</Text>
            </TouchableOpacity>
            <TouchableOpacity
              style={styles.thirdPartyButton}
              onPress={() => handleQuickLogin('支付宝')}>
              <Text style={styles.thirdPartyIcon}>💰</Text>
              <Text style={styles.thirdPartyText}>支付宝</Text>
            </TouchableOpacity>
          </View>
        </View>

        {/* 测试提示 */}
        <View style={styles.testHint}>
          <Text style={styles.testHintText}>测试账号</Text>
          <Text style={styles.testHintDetail}>手机号：13888888888</Text>
          <Text style={styles.testHintDetail}>密码：123456</Text>
        </View>
      </ScrollView>
    </KeyboardAvoidingView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  scrollContainer: {
    flexGrow: 1,
    padding: 20,
  },
  logoContainer: {
    alignItems: 'center',
    marginTop: 60,
    marginBottom: 40,
  },
  logo: {
    fontSize: 80,
    marginBottom: 16,
  },
  appName: {
    fontSize: 28,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 8,
  },
  slogan: {
    fontSize: 16,
    color: '#666',
  },
  formContainer: {
    backgroundColor: 'white',
    borderRadius: 16,
    padding: 24,
    marginBottom: 24,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 5,
  },
  inputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
    marginBottom: 20,
    paddingBottom: 12,
  },
  inputIcon: {
    fontSize: 20,
    marginRight: 12,
  },
  input: {
    flex: 1,
    fontSize: 16,
    color: '#333',
    paddingVertical: 8,
  },
  loginButton: {
    backgroundColor: '#007AFF',
    borderRadius: 12,
    padding: 16,
    alignItems: 'center',
    marginTop: 20,
    marginBottom: 20,
  },
  loginButtonDisabled: {
    backgroundColor: '#ccc',
  },
  loginButtonText: {
    color: 'white',
    fontSize: 18,
    fontWeight: 'bold',
  },
  linkContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  linkText: {
    color: '#007AFF',
    fontSize: 14,
  },
  thirdPartyContainer: {
    alignItems: 'center',
    marginBottom: 30,
  },
  thirdPartyTitle: {
    fontSize: 16,
    color: '#666',
    marginBottom: 20,
  },
  thirdPartyButtons: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    width: '100%',
  },
  thirdPartyButton: {
    alignItems: 'center',
    padding: 16,
    backgroundColor: 'white',
    borderRadius: 12,
    minWidth: 80,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  thirdPartyIcon: {
    fontSize: 32,
    marginBottom: 8,
  },
  thirdPartyText: {
    fontSize: 14,
    color: '#333',
  },
  testHint: {
    backgroundColor: '#fff3cd',
    borderRadius: 8,
    padding: 16,
    alignItems: 'center',
  },
  testHintText: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#856404',
    marginBottom: 8,
  },
  testHintDetail: {
    fontSize: 14,
    color: '#856404',
    marginBottom: 2,
  },
});

export default LoginScreen;
