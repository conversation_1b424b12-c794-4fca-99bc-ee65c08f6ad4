import React from 'react';
import { createNativeStackNavigator } from '@react-navigation/native-stack';

// 导入导航器和页面
import TabNavigator from './TabNavigator';
import LoginScreen from '../screens/LoginScreen';

const Stack = createNativeStackNavigator();

const AppNavigator = () => {
  return (
    <Stack.Navigator
      screenOptions={{
        headerStyle: {
          backgroundColor: '#007AFF',
        },
        headerTintColor: 'white',
        headerTitleStyle: {
          fontWeight: 'bold',
        },
      }}>
      {/* 主要的Tab导航 */}
      <Stack.Screen
        name="Main"
        component={TabNavigator}
        options={{
          headerShown: false, // 隐藏Stack的header，使用Tab的header
        }}
      />
      
      {/* 登录页面 */}
      <Stack.Screen
        name="Login"
        component={LoginScreen}
        options={{
          title: '登录',
          headerBackTitle: '返回',
        }}
      />
    </Stack.Navigator>
  );
};

export default AppNavigator;
