# 购物商城应用功能说明

## 新增功能

### 1. 启动页 (SplashScreen)
- **位置**: `src/screens/SplashScreen.tsx`
- **功能**: 
  - 应用启动时显示商城Logo
  - 包含淡入和缩放动画效果
  - 加载点动画（三个点依次闪烁）
  - 模拟初始化操作（2.5秒）
  - 初始化完成后自动跳转到主页面
- **设计**:
  - 蓝色背景 (#007AFF)
  - 居中显示大号购物袋图标 🛍️
  - "购物商城" 标题和 "发现更多精彩" 副标题
  - 底部版权信息和版本号

### 2. iOS风格路由动画
- **配置位置**: `src/navigation/AppNavigator.tsx`
- **动画效果**:
  - 页面切换使用 `slide_from_right` 动画（右进左出）
  - 支持手势返回（水平滑动）
  - 卡片式页面展示
- **特殊配置**:
  - 启动页禁用手势返回
  - 主页面禁用手势返回
  - 登录页面支持手势返回

### 3. 优化的Tab导航
- **位置**: `src/navigation/TabNavigator.tsx`
- **iOS适配**:
  - iOS设备底部安全区域适配
  - Tab栏高度自动调整
  - iOS风格的切换动画

## 应用流程

1. **启动流程**:
   ```
   SplashScreen (2.5秒) → Main (TabNavigator)
   ```

2. **导航结构**:
   ```
   AppNavigator (Stack)
   ├── Splash (启动页)
   ├── Main (Tab导航)
   │   ├── Home (首页)
   │   ├── Category (分类)
   │   ├── Cart (购物车)
   │   └── Profile (我的)
   └── Login (登录页)
   ```

3. **页面跳转**:
   - 启动页 → 主页面：自动跳转（reset导航栈）
   - 我的页面 → 登录页：手动跳转（push）
   - 登录页 → 我的页面：返回（pop）

## 技术特性

### 动画效果
- **启动页动画**:
  - Logo淡入 + 缩放弹性动画
  - 加载点循环闪烁动画
  - 退出淡出动画
- **路由动画**:
  - iOS原生风格的右进左出
  - 支持手势返回
  - 流畅的页面切换

### 平台适配
- **iOS**:
  - 安全区域适配
  - 原生动画效果
  - 手势支持
- **Android**:
  - 相同的动画效果
  - 适配Android导航栏

### 代码结构
- 模块化设计
- TypeScript类型支持
- 清晰的文件组织
- 可维护的代码结构

## 使用说明

1. **启动应用**: 会先显示启动页，2.5秒后自动进入主页面
2. **页面导航**: 使用底部Tab切换主要功能
3. **登录功能**: 在"我的"页面点击登录按钮跳转到登录页
4. **返回操作**: 支持iOS风格的手势返回和返回按钮

## 测试建议

1. 测试启动页动画效果
2. 测试页面切换动画
3. 测试手势返回功能
4. 测试不同设备的适配效果
